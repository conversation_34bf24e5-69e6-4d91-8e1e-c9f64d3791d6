private fun showStickyBottomTabWithoutAnimation() {
        if (isAnimating || isBottomTabVisible || stickyBottomTabContainer != null) return

        android.util.Log.d("StickyBottomTabPlugin", "开始创建初始吸底Tab（无动画）")
        isBottomTabVisible = true

        // 创建吸底Tab容器
        stickyBottomTabContainer = createStickyTabContainer()
        val text = TextView(context)
        text.setText("测试")
        text.setTextColor(context.resources.getColor(R.color.COLOR_FF842C))
//        val stickyTabLayout = createStickyTabLayout()
        stickyBottomTabContainer?.addView(text)

        // 添加到CoordinatorLayout底部，考虑导航栏适配
        val layoutParams = CoordinatorLayout.LayoutParams(
            CoordinatorLayout.LayoutParams.MATCH_PARENT,
            CoordinatorLayout.LayoutParams.WRAP_CONTENT
        ).apply {
            gravity = Gravity.BOTTOM
            // 设置底部边距以避开导航栏
            bottomMargin = 200
            // 设置水平边距以避开侧边导航栏
            leftMargin = config.horizontalMargin
            rightMargin = config.horizontalMargin
        }

        config.coordinatorLayout.addView(stickyBottomTabContainer, layoutParams)

        // 直接显示，无动画
        stickyBottomTabContainer?.translationY = 0f

        android.util.Log.d("StickyBottomTabPlugin", "初始吸底Tab显示完成")

        // 设置状态同步
//        setupTabSync(stickyTabLayout)
    }。这块代码的问题。自己加一个textview可以正常显示。。。。这段信息是客户的反馈