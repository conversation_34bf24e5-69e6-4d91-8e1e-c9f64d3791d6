package com.hbg.lib.widgets;

import android.annotation.SuppressLint;
import android.content.Context;
import android.util.AttributeSet;
import android.view.View;
import android.widget.RelativeLayout;
import com.hbg.lib.common.utils.PixelUtils;
import net.lucode.hackware.magicindicator.buildins.commonnavigator.abs.IPagerTitleView;
import net.lucode.hackware.magicindicator.buildins.commonnavigator.titles.SimplePagerTitleView;

public class RedPointPagerTitleView extends RelativeLayout implements IPagerTitleView {

    private View point;
    private SimplePagerTitleView simplePagerTitleView;

    public RedPointPagerTitleView(Context context) {
        super(context);
    }

    public RedPointPagerTitleView(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    public RedPointPagerTitleView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    @SuppressLint("ResourceType")
    public void setColorTransitionView(SimplePagerTitleView view, boolean needPoint) {
        simplePagerTitleView = view;
        LayoutParams layoutParams = new LayoutParams(LayoutParams.WRAP_CONTENT, LayoutParams.WRAP_CONTENT);
        layoutParams.addRule(RelativeLayout.CENTER_IN_PARENT);
        simplePagerTitleView.setLayoutParams(layoutParams);
        if(simplePagerTitleView.getId() == View.NO_ID) {
            simplePagerTitleView.setId(0x123);
        }
        addView(simplePagerTitleView);
        if (needPoint) {
            LayoutParams pointParams = new LayoutParams(PixelUtils.dp2px(7f), PixelUtils.dp2px(7f));
            pointParams.addRule(RelativeLayout.RIGHT_OF, simplePagerTitleView.getId());
            pointParams.addRule(RelativeLayout.ABOVE, simplePagerTitleView.getId());
            pointParams.bottomMargin = -PixelUtils.dp2px(5f);
            pointParams.leftMargin = -PixelUtils.dp2px(3f);
            point = new View(getContext());
            point.setLayoutParams(pointParams);
            point.setBackgroundResource(R.drawable.red_point);
            point.setVisibility(View.GONE);
            addView(point);
        }
    }

    /**
     * 显示红点
     */
    public void showPoint() {
        if (point != null) point.setVisibility(View.VISIBLE);
    }

    /**
     * 隐藏红点
     */
    public void hidePoint() {
        if (point != null) point.setVisibility(View.GONE);
    }

    /**
     * 获取SimplePagerTitleView
     *
     * @return simplePagerTitleView
     */
    public SimplePagerTitleView getSimplePagerTitleView() {
        return simplePagerTitleView;
    }

    @Override
    public void onSelected(int index, int totalCount) {
        if (simplePagerTitleView != null) {
            simplePagerTitleView.onSelected(index, totalCount);
        }
    }

    @Override
    public void onDeselected(int index, int totalCount) {
        if (simplePagerTitleView != null) {
            simplePagerTitleView.onDeselected(index, totalCount);
        }
    }

    @Override
    public void onLeave(int index, int totalCount, float leavePercent, boolean leftToRight) {
        if (simplePagerTitleView != null) {
            simplePagerTitleView.onLeave(index, totalCount, leavePercent, leftToRight);
        }
    }

    @Override
    public void onEnter(int index, int totalCount, float enterPercent, boolean leftToRight) {
        if (simplePagerTitleView != null) {
            simplePagerTitleView.onEnter(index, totalCount, enterPercent, leftToRight);
        }
    }
}
