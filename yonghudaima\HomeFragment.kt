package com.huobi.home.ui

import android.Manifest
import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.animation.ValueAnimator
import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.graphics.Color
import android.graphics.Typeface
import android.net.Uri
import android.os.Bundle
import android.text.TextUtils
import android.util.Log
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.animation.DecelerateInterpolator
import android.view.animation.AccelerateDecelerateInterpolator
import android.view.animation.OvershootInterpolator
import android.widget.CheckBox
import android.widget.FrameLayout
import android.widget.LinearLayout
import android.widget.RelativeLayout
import android.widget.TextView
import androidx.annotation.Keep
import androidx.coordinatorlayout.widget.CoordinatorLayout
import androidx.coordinatorlayout.widget.CoordinatorLayout.LayoutParams
import androidx.core.content.ContextCompat
import androidx.core.content.res.ResourcesCompat
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import com.google.android.material.appbar.AppBarLayout
import androidx.lifecycle.lifecycleScope
import androidx.viewpager2.widget.ViewPager2
import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONObject
import com.business.common.red_packet.RedPacketManager
import com.google.android.material.appbar.AppBarLayout
import com.google.zxing.client.android.CaptureActivity
import com.hbg.lib.common.utils.DateTimeUtils
import com.hbg.lib.common.utils.DebugLog
import com.hbg.lib.common.utils.HUIHandler
import com.hbg.lib.common.utils.SP
import com.hbg.lib.common.utils.crypt.MD5Utils
import com.hbg.lib.core.BaseModuleConfig
import com.hbg.lib.core.constants.ConfigConstant
import com.hbg.lib.core.model.ContractOverviewController
import com.hbg.lib.core.network.rx.EasySubscriber
import com.hbg.lib.core.page.SmartRefreshFooter
import com.hbg.lib.core.permissions.EasyPermissions
import com.hbg.lib.core.permissions.EasyPermissions.PermissionCallbacks
import com.hbg.lib.core.ui.BaseActivity
import com.hbg.lib.core.util.AppLanguageHelper
import com.hbg.lib.core.util.NightHelper
import com.hbg.lib.core.util.PhoneUtils
import com.hbg.lib.core.util.RxJavaHelper
import com.hbg.lib.core.util.ThemeHelper
import com.hbg.lib.core.webview.HBBaseWebActivity
import com.hbg.lib.network.hbg.HbgGlobalApi
import com.hbg.lib.network.hbg.core.bean.TokenBindInfo
import com.hbg.lib.network.linear.swap.controller.LinearSwapOverviewController
import com.hbg.lib.network.pro.HbgProApi
import com.hbg.lib.network.pro.core.bean.ProTokenUpdate
import com.hbg.lib.network.pro.socket.listener.LastKlineListener
import com.hbg.lib.network.pro.socket.listener.MarketOverviewListenerV2
import com.hbg.lib.network.pro.socket.response.LastKlineResponse
import com.hbg.lib.network.pro.socket.response.MarketOverviewV2Response
import com.hbg.lib.network.retrofit.exception.APIStatusErrorException
import com.hbg.lib.network.swap.core.controller.SwapOverviewController
import com.hbg.lib.widgets.utils.HuobiToastUtil
import com.hbg.module.content.ui.fragment.NewsChildFragment
import com.hbg.module.huobi.im.RedPoint.RedPointHelper
import com.hbg.module.libkt.base.adapter.BasePageAdapter
import com.hbg.module.libkt.base.ext.toGsonStr
import com.hbg.module.libkt.custom.indicator.CoIndicator
import com.hbg.module.libkt.custom.indicator.IndicatorHelper
import com.hbg.module.libkt.custom.indicator.TabClickListener
import com.hbg.module.libkt.custom.indicator.TabData
import com.hbg.module.libkt.custom.indicator.navigator.CommonNavigator
import com.hbg.module.libkt.custom.indicator.navigator.titles.RedPointPagerTitleView
import com.hbg.module.libkt.utils.event.LiveDataBus.with
import com.hbg.module.libkt.utils.event.LiveKeyCons
import com.hbg.module.livesquare.ui.LiveSquareHomeFragment
import com.hbg.module.livesquare.utils.LiveTrackUtils
import com.huobi.R
import com.huobi.account.event.LogOutEvent
import com.huobi.account.helper.UserProvider
import com.huobi.account.ui.SecuritySetActivity
import com.huobi.account.ui.VerificationStartActivity
import com.huobi.apm.AppStartMonitorManager
import com.huobi.edgeengine.EdgeEngine
import com.huobi.edgeengine.node.DataCallback
import com.huobi.finance.api.RiskService
import com.huobi.finance.bean.TsvMsg
import com.huobi.home.data.HomeContentTabRedPointEvent
import com.huobi.home.data.HomepageConfig
import com.huobi.home.data.TransferAmountInfo
import com.huobi.home.engine.HomeBridgeAbility
import com.huobi.home.engine.HomeEngineCore
import com.huobi.home.engine.HomeEngineEvent
import com.huobi.home.presenter.HomePresenter
import com.huobi.homemarket.model.ProOverviewController
import com.huobi.index.bean.IndexInformationRequestData
import com.huobi.index.bean.IndexInformationRequestData.ACTION_REFRESH
import com.huobi.index.bean.IndexInformationRequestData.ACTION_UP
import com.huobi.index.helper.IndexHelper
import com.huobi.index.helper.LoginTokenHelper
import com.huobi.index.trace.IndexLifeCycleStep
import com.huobi.index.trace.IndexLifeCycleTracer
import com.huobi.index.ui.AnnouncementFragment
import com.huobi.index.ui.FeedFragment
import com.huobi.index.ui.ScanLoginSuccessActivity
import com.huobi.login.bean.IAuthTarget
import com.huobi.login.usercenter.external.UserCenterActionHelper
import com.huobi.main.helper.HBHomeSkinHelper
import com.huobi.main.navigator.JumpUtils
import com.huobi.page.SmartRefreshHeader
import com.huobi.retrofit.HRetrofit
import com.huobi.statistics.SensorsDataHelper
import com.huobi.utils.ScanUtils
import com.huobi.view.MyNestedScrollView
import com.scwang.smartrefresh.layout.SmartRefreshLayout
import com.scwang.smartrefresh.layout.api.RefreshLayout
import com.scwang.smartrefresh.layout.constant.RefreshState
import com.scwang.smartrefresh.layout.listener.OnRefreshLoadMoreListener
import com.scwang.smartrefresh.layout.listener.SimpleMultiPurposeListener
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import java.io.Serializable
import java.util.concurrent.ConcurrentHashMap
import kotlin.math.abs

/**
 *@Description:
 *@Author: wangyuxi
 *@Date: 2023/4/13
 */
class HomeFragment : BaseHomeFragment<HomePresenter, HomePresenter.HomeUI>(), HomePresenter.HomeUI, PermissionCallbacks {

    val TAG = "HomeFragment"

    // webview pool container
    private var mWebviewPoolContainer: FrameLayout? = null

    //navigation 区域
    private var mNavigation: RelativeLayout? = null
    private var mNavigationContainer: LinearLayout? = null

    //下拉刷新
    private var mRefreshLayout: SmartRefreshLayout? = null

    //下拉刷新头
    private var mCustomHeader: SmartRefreshHeader? = null

    //滚动组件
    private var mFluentScrollView: MyNestedScrollView? = null
    private var clLayout: CoordinatorLayout? = null
    private var appBarLayout: AppBarLayout? = null

    //    private var mFluentScrollerLayout: ConsecutiveScrollerLayout? = null
    private var mFluentLayout: LinearLayout? = null

    //内容feed
    private var coIndicator: CoIndicator? = null
    private var mFeedViewPager: ViewPager2? = null
    private var mFeedTabLayoutGroup: LinearLayout? = null
    private var mFeedReleaseBtn: View? = null
    private var feedFragmentList: ArrayList<Fragment>? = null

    //行情数据监听
    private var mProListener: MarketOverviewListenerV2? = null

    //端引擎
    private var mEdgeEngine: EdgeEngine? = null

    private var homepageConfig: HomepageConfig? = null
    private var transferAmountInfo: TransferAmountInfo? = null

    private var homeAnimation: ViewGroup? = null

    private var isTopIcon = false
    private var homeRadioContainer: ViewGroup? = null
    private var homeCheckBox: CheckBox? = null
    private var homeCheckBoxBg: CheckBox? = null
    private var homeCheckBoxIcon: CheckBox? = null
    private var animator: ValueAnimator? = null
    private var mScrollY = 0
    private var mIsGetProToken = false
    private var feedScroll = false
    private var lastScrollUpdate: Long = -1

    private var nowCurrentContentPos = -1
    var tabs: ArrayList<TabData>? = null

    // 吸底Tab相关变量
    private var stickyBottomTabContainer: LinearLayout? = null
    private var stickyCoIndicator: CoIndicator? = null  // 吸底CoIndicator实例
    private var isBottomTabVisible = false
    private var isAnimating = false
    private var isInitialized = false  // 添加初始化标志
    private var isHandlingTabClick = false  // 添加点击处理标志，防止滚动监听器干扰
    private var navigationBarHeight: Int = 0
    private var actualBottomMargin: Int = 0
    private val animationDuration: Long = 400L  // 增加动画时长，使其更柔和
    private val scrollToPosition: Float = 0.33f  // 滚动到屏幕的1/3位置

    // 自然切换相关变量
    private var lastSwitchTime = 0L  // 上次切换时间，用于防抖动
    private val switchDebounceDelay = 50L  // 切换防抖动延迟（毫秒），减少延迟使隐藏更及时

    override fun createView(inflater: LayoutInflater, container: ViewGroup, savedInstanceState: Bundle?): View {
        val layoutRes: Int = getAppropriateLayoutResId()
        if (!EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().register(this)
        }
        //啄木鸟首页启动埋点
        IndexLifeCycleTracer.getInstance().report(IndexLifeCycleStep.AppHomePage)
        HomeEngineCore.navModuleViews.clear()
        HomeEngineCore.fluentModuleViews.clear()
        return ThemeHelper.instance.getNewThemeView(requireActivity(), inflater, layoutRes, container)
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        setRootViewOnly(null)
        return super.onCreateView(inflater, container, savedInstanceState)
    }

    override fun onAttach(context: Context) {
        super.onAttach(context)
        presenter.getHomePageConfig()
        presenter.refreshContract()
    }

    override fun getUI(): HomePresenter.HomeUI {
        return this
    }

    override fun createPresenter(): HomePresenter {
        return HomePresenter()
    }

    @SuppressLint("RestrictedApi")
    override fun initViews() {
        mWebviewPoolContainer = viewFinder.find(R.id.webview_pool_container)

        mNavigation = viewFinder.find(R.id.navigation_container)
        mNavigationContainer = viewFinder.find(R.id.home_navigation_ll)
        val statusBar = viewFinder.find<View>(R.id.home_status_bar)
        val params = statusBar.layoutParams
        params.height = BaseActivity.getStatusBarHeight(statusBar.context)
        statusBar.layoutParams = params

        mRefreshLayout = viewFinder.find(R.id.fluent_refresh_layout)
        clLayout = viewFinder.find(R.id.clLayout)
        appBarLayout = viewFinder.find(R.id.appBarLayout)

        // 初始化吸底Tab相关配置
        initStickyBottomTab()
//        mFluentScrollerLayout = viewFinder.find(R.id.fluent_scrollerlayout)
        mFluentScrollView = viewFinder.find(R.id.fluent_content_nsv)
        mFluentLayout = viewFinder.find(R.id.fluent_container)

        homeAnimation = viewFinder.find(R.id.rl_new_hand_area_animation_layer)

        initEngine()
        initViewPager()
        initWifiInfo(activity)
        setRefreshLayout()

        mRefreshLayout?.setOnMultiPurposeListener(object : SimpleMultiPurposeListener() {
//            override fun onFooterReleasing(footer: RefreshFooter, percent: Float, offset: Int, footerHeight: Int, extendHeight: Int) {
//            }
//
//            override fun onFooterPulling(footer: RefreshFooter, percent: Float, offset: Int, footerHeight: Int, extendHeight: Int) {
//                mFluentScrollerLayout?.stickyOffset = offset
//            }
        })

        activity?.let {
            homeRadioContainer = it.findViewById(R.id.main_home_tab)
            homeRadioContainer?.run {
                homeCheckBox = this.findViewById(R.id.main_index_cb)
                homeCheckBoxBg = this.findViewById(R.id.main_index_cb_bg)
                homeCheckBoxIcon = this.findViewById(R.id.main_index_cb_icon)
                val remoteSkinBean = HBHomeSkinHelper.getInstance().getRealRemoteSkinBean(it)
                val isNightMode = NightHelper.getInstance().isNightMode
                remoteSkinBean?.let { bean ->
                    bean.tabbar?.let { bar ->
                        val tabBarBean = if (isNightMode) {
                            bar.night
                        } else {
                            bar.light
                        }
                        tabBarBean?.let {
                            val homeIcon = tabBarBean.home.icon
                            val rocketIcon = tabBarBean.rocket.rocket_icon
                            val rocketBg = tabBarBean.rocket.rocket_bg
                            homeCheckBoxBg?.buttonDrawable = HBHomeSkinHelper.getInstance()
                                .getDrawable(R.drawable.tab_bg_feed_top_bg, rocketBg, homeIcon)
                            homeCheckBoxIcon?.buttonDrawable = HBHomeSkinHelper.getInstance()
                                .getDrawable(R.drawable.tab_bg_feed_top_icon, rocketIcon, homeIcon)
                        }
                    }
                }
            }
        }

        AppStartMonitorManager.getInstance().mainAppear();
    }

    fun checkFutureSub() {
//        目前首页没用到合约的推送 取消订阅 解决安卓首页卡顿，闪退问题
//        val canBeSeen = ui.isCanBeSeen
//        if (canBeSeen) {
//            subscribeFutureMarketWebSocket()
//        } else {
//            unsubscribeFutureMarketWebSocket()
//        }
    }

    /**
     * 合约行情ws订阅
     */
    private fun subscribeFutureMarketWebSocket() {
        DebugLog.i("FutureRank", "subscribeFutureMarketWebSocket")
        ContractOverviewController.getInstance().subOverview()
        SwapOverviewController.getInstance().subOverview()
        LinearSwapOverviewController.getInstance().subOverview()
    }

    /**
     * 合约行情ws退订
     */
    private fun unsubscribeFutureMarketWebSocket() {
        DebugLog.i("FutureRank", "unsubscribeFutureMarketWebSocket")
        ContractOverviewController.getInstance().unSubOverView()
        SwapOverviewController.getInstance().unSubOverView()
        LinearSwapOverviewController.getInstance().unSubOverView()
    }

    override fun addEvent() {
//        registMarketListener()
        registLogin()
        homeIconEvent()
    }


    override fun afterInit() {
    }


    override fun onDestroyView() {
        mFeedViewPager?.adapter = null
        // 清理吸底Tab
        cleanupStickyBottomTab()
        super.onDestroyView()
    }

    override fun onDestroy() {
        super.onDestroy()
        EventBus.getDefault().unregister(this)
        if (mEdgeEngine != null) {
            mEdgeEngine!!.release()
            mEdgeEngine = null
        }
//        unRegistMarketListener()
        stopUpdateRankingTimer()
        unregistRankListener()
    }

    private fun getAppropriateLayoutResId(): Int {
        SP.set(IndexHelper.KEY_HOME_PAGE_LAYOUT_TYPE, "A")
        return R.layout.fragment_home
    }

    /***--------------Engine----------------------***/
    fun initEngine() {
        mEdgeEngine = EdgeEngine(requireActivity(), "home")
        mEdgeEngine?.registerAbility("homeBridge", HomeBridgeAbility::class.java)
//        mEdgeEngine?.registerWidget(AnimTextViewWidget.KEY, AnimTextViewWidget::class.java)
        mEdgeEngine?.registerWidget(HomeAssetTextView.KEY, HomeAssetTextView::class.java)
        mEdgeEngine?.registerWidget(HomeHBHomeCubeLivingView.KEY,HomeHBHomeCubeLivingView::class.java)
        mEdgeEngine?.registerWidget(HomeCubeCompositeView.KEY,HomeCubeCompositeView::class.java)
        mEdgeEngine?.runScript()
        mEdgeEngine?.sendEvent("initEngine()")
        sendCommonEvents()
        initRankListener()
    }

    fun sendCommonEvents() {
        HomeEngineEvent.sendSymbolInfo(mEdgeEngine)
        HomeEngineEvent.sendLoginStatus(mEdgeEngine)
        HomeEngineEvent.sendCommonConfig(mEdgeEngine)
        HomeEngineEvent.sendRateTypeStr(mEdgeEngine)

        HomeEngineEvent.isConditionMet.observe(this) { isTrue ->
            if (isTrue) {
                // 执行后续操作
                HomeEngineCore.refreshHomeView(mEdgeEngine,activity)
            }
        }
    }

    /***--------------UI----------------------***/
    override fun lodaHomeUI(homepageConfig: HomepageConfig?, userinfo: TransferAmountInfo?) {
        if (context == null || (context is FragmentActivity && (context as FragmentActivity).isFinishing)) {
            return
        }

        this.transferAmountInfo = userinfo
        if (this.homepageConfig == null) {
            Log.d("Home", "homepageConfig == null")
            this.homepageConfig = homepageConfig
            mFluentLayout?.removeAllViews()
            mNavigationContainer?.removeAllViews()
            HomeEngineCore.startRenderHome(
                mEdgeEngine,
                requireActivity(),
                mNavigationContainer,
                mFluentLayout,
                homepageConfig,
                userinfo
            )
        } else {
            this.homepageConfig = homepageConfig
            HomeEngineCore.refreshHomeView(
                mEdgeEngine,
                requireActivity(),
                mNavigationContainer,
                mFluentLayout,
                homepageConfig,
                userinfo
            )
            mRefreshLayout?.finishRefresh()
        }
    }

    override fun sendSymbolInfo() {
        HomeEngineEvent.sendSymbolInfo(mEdgeEngine)
    }

    /**
     * 设置刷新
     */
    fun setRefreshLayout() {
        mRefreshLayout?.isEnableRefresh = true
        mRefreshLayout?.isEnableLoadMore = true
        mRefreshLayout?.setEnableLoadMoreWhenContentNotFull(false)
        mRefreshLayout?.setRefreshFooter(SmartRefreshFooter(activity))
        mCustomHeader = SmartRefreshHeader(activity)
        mRefreshLayout?.setRefreshHeader(mCustomHeader!!)
        mRefreshLayout?.setOnRefreshLoadMoreListener(object : OnRefreshLoadMoreListener {
            override fun onLoadMore(refreshLayout: RefreshLayout) {
                DebugLog.i("home -----加载更多....")
                refreshFeedData(ACTION_UP)
//                SensorsDataHelper.track(
//                    "app_news_rechome_nrkpsl",
//                    HomeSensorsHelper.getFeedShowMap( HomeHelper.getCurrentTabType( mFeedViewPager, feedFragmentList))
//                )
            }

            override fun onRefresh(refreshLayout: RefreshLayout) {
                sendCommonEvents()
                //页面可见状态下拉刷新请求数据，否则停止刷新
                if (ui.isCanBeSeen) {
//                    HomeEngineCore.refreshHomeView(mEdgeEngine,activity)
//                    HomeEngineEvent.sendRefreshData(mEdgeEngine,)
                    presenter.getHomePageConfig()
                    refreshFeedData(ACTION_REFRESH)
//                    HomeEngineEvent.sendRefreshData(mEdgeEngine)
//                    SensorsDataHelper.track(
//                        "app_news_rechome_nrkpxl",
//                        HomeSensorsHelper.getFeedShowMap(HomeHelper.getCurrentTabType(mFeedViewPager, feedFragmentList)))
                } else {
                    ptrRefreshFinished(true)
                }
            }
        })
    }

    fun ptrRefreshFinished(refresh: Boolean) {
        val nowTime = DateTimeUtils.getDateTimeNow()
        val strTime = DateTimeUtils.formatStr(nowTime, DateTimeUtils.FORMAT_MM_DD_HH_MM_SS)
        mCustomHeader!!.setLastUpdateText(strTime)
        if (refresh) {
            mRefreshLayout?.finishRefresh()
            mRefreshLayout?.setNoMoreData(false)
        } else {
            mRefreshLayout?.finishLoadMore()
        }
    }

    fun isRefreshing(): Boolean {
        return mRefreshLayout?.isRefreshing ?: false
    }

    fun setRefreshState() {
        mRefreshLayout?.finishLoadMore()
    }

    private fun isLoading(): Boolean {
        return mRefreshLayout?.state == RefreshState.Loading
    }

    /***--------------事件监听----------------------***/
//    private fun registMarketListener() {
//        if (mProListener == null) {
//            mProListener = object : MarketOverviewListenerV2() {
//                override fun onSuccess(response: MarketOverviewV2Response) {
//                    if (response == null || response.data == null) return
//                    Log.d("Home", "------------MarketOverviewListenerV2---------------")
//                    val result = JSONObject()
//                    for ((key, symbolPriceV2) in response.data) {
//                        if (symbolPriceV2 == null) continue
//                        val item = JSONObject()
//                        item["decimalcPrice"] = symbolPriceV2.close
//                        item["decimalDelta"] = symbolPriceV2.rise * 100
//                        item["strAmount"] = symbolPriceV2.amount
//                        item["symbol"] = symbolPriceV2.symbol
//                        result[key] = item
//                    }
//                    HomeEngineEvent.sendSocketData(mEdgeEngine, "market", result)
//                }
//            }
//        }
//        HbgProApi.getAPI().subscribeMarketOverview(true, mProListener)
//    }

//    private fun unRegistMarketListener() {
//        if (mProListener == null) return
//        HbgProApi.getAPI().subscribeMarketOverview(false, mProListener)
//    }

    private var mRankCache: ConcurrentHashMap<String, LastKlineResponse> = ConcurrentHashMap()
    private var mLastSymbols = mutableListOf<String>()
    private var mRankListener: LastKlineListener? = null
    private var mUpdateRankingInterval = 0L//0=不限制，1=限制频率1秒更新1次
    private fun initRankListener() {
        mEdgeEngine?.registerDataCallback("subRankingData", object : DataCallback {
            override fun onCallback(value: Any?) {
                try {
                    if (value == null) return
                    Log.d("homeFragment", "subRankingData value= ${value}")
                    val jsonObject = JSON.parseObject(value.toString())
                    if (jsonObject.containsKey("sub")) {
                        val subList: List<String> =
                            jsonObject.getJSONArray("sub").toJavaList(String::class.java)
                        if (subList.equals(mLastSymbols)) {
                            Log.d("homeFragment", "subRankingData subList is same, no need to update")
                            return
                        }
                        unregistRankListener()
                        mLastSymbols.clear()
                        mLastSymbols.addAll(subList)
                        registRankListener()
                    }
                } catch (e: Throwable) {
                    e.printStackTrace()
                }
            }
        })
        mEdgeEngine?.registerDataCallback("updateRankingInterval", object : DataCallback {
            override fun onCallback(value: Any?) {
                try {
                    Log.d("homeFragment", "updateRankingInterval value= ${value}")
                    if (value == null) return
                    val interval: Float? = value.toString().toFloatOrNull()
                    if (interval == null) return
                    val temp = (interval * 1000L).toLong()
                    if (mUpdateRankingInterval == temp) return
                    mUpdateRankingInterval = temp
                    if (mUpdateRankingInterval > 0) {
                        startUpdateRankingTimer()
                    }
                } catch (e: Throwable) {
                    e.printStackTrace()
                }
            }
        })
    }
    private var mTimer: Job? = null
    private fun startUpdateRankingTimer() {
        stopUpdateRankingTimer()
        mTimer = lifecycleScope.launch {
            while (true) {
                Log.d("homeFragment", "startUpdateRankingTimer mUpdateRankingInterval= ${mUpdateRankingInterval} mRankCache= ${mRankCache.size}")
                if (mRankCache.isNotEmpty()) {
                    val result = JSONObject()
                    for ((key, resp) in mRankCache) {
                        if (resp == null) continue
                        val item = JSONObject()
                        item["decimalcPrice"] = resp.tick.close
                        item["decimalDelta"] = resp.tick.changeRatio * 100
                        item["strAmount"] = resp.tick.amount
                        item["symbol"] = resp.symbol
                        result[resp.symbol] = item
                    }
                    HomeEngineEvent.sendSocketData(mEdgeEngine, "market", result)
                }
                delay(mUpdateRankingInterval)
            }
        }
    }
    private fun stopUpdateRankingTimer() {
        if (mTimer != null && mTimer?.isActive == true) {
            mTimer?.cancel()
        }
    }

    private fun registRankListener() {
        if (mLastSymbols.isEmpty()) {
            return
        }
        if (mUpdateRankingInterval > 0 && mTimer?.isActive != true) {
            startUpdateRankingTimer()
        }
        if (mRankListener == null) {
            mRankListener = object : LastKlineListener() {
                override fun onSuccess(response: LastKlineResponse) {
                    mRankCache.put(response.symbol, response)
                    Log.d("homeFragment", "subscribeBatch onSuccess symbol= ${response.symbol} mRankCache= ${mRankCache.size}")
                    //间隔大于0，走定时器更新
                    if (mUpdateRankingInterval > 0L) return
                    val result = JSONObject()
                    val item = JSONObject()
                    item["decimalcPrice"] = response.tick.close
                    item["decimalDelta"] = response.tick.changeRatio * 100
                    item["strAmount"] = response.tick.amount
                    item["symbol"] = response.symbol
                    result[response.symbol] = item
                    HomeEngineEvent.sendSocketData(mEdgeEngine, "market", result)
                }
            }
        }
        HbgProApi.getAPI().subscribeBatch(
            true,
            mLastSymbols,
            mRankListener
        )
    }

    private fun unregistRankListener() {
        if (mLastSymbols.isEmpty()) {
            return
        }
        HbgProApi.getAPI().subscribeBatch(false, mLastSymbols, mRankListener)
        mRankCache.clear()
    }

    private fun initRedPointListener() {
        RedPointHelper.getInstance().setChatListRedPointUiUpdateListener { baseRedPointNode ->
            val redCount = baseRedPointNode.redCount
            HomeEngineEvent.sendUnreadMessage(mEdgeEngine, redCount)
        }
    }

    private fun registLogin() {
        //登录成功回调
        with(LiveKeyCons.HOME_FEED_REFRESH, Int::class.java).observe(this) { integer: Int? ->
            if (context == null || (context is FragmentActivity && (context as FragmentActivity).isFinishing)) {
                // is nothing todo it
            } else {
                HomeEngineEvent.sendLoginStatus(mEdgeEngine)
                presenter.getTransferAmountInfo()
                HomeEngineCore.refreshHomeView(mEdgeEngine, requireActivity())
            }
        }

        with(LiveKeyCons.HOME_TAB_CHANGE, Int::class.java).observe(this) { index: Int? ->
            if (context == null || (context is FragmentActivity && (context as FragmentActivity).isFinishing)) {
                // is nothing todo it
            } else {
                index?.let {
                    mFeedViewPager?.setCurrentItem(index, false)
                }
            }
        }

        with(LiveKeyCons.HOME_CONTENT_TAB_RED_POINT, HomeContentTabRedPointEvent::class.java).observeStick(
            this,
            { event: HomeContentTabRedPointEvent? ->
                Log.d("HomeFragment", "${LiveKeyCons.HOME_CONTENT_TAB_RED_POINT} event : ${event.toGsonStr}")
                if (context == null || (context is FragmentActivity && (context as FragmentActivity).isFinishing)) {
                    // is nothing
                } else {
                    event?.let { it ->
                        changeRedPoint(it.index, it.isShow)
                    }
                }
            })
    }

    /**
     * 滑动状态处理
     */
    private val scrollStateHandler: Runnable = object : Runnable {
        override fun run() {
            val currentTime = System.currentTimeMillis()
            if (currentTime - lastScrollUpdate > 100) {
                lastScrollUpdate = -1
                feedScroll = false
            } else {
                HUIHandler.getInstance().postDelayed(this, 100)
            }
        }
    }

    fun isScroll(): Boolean {
        return feedScroll
    }

    private var viewHeight = 0
    private var verticalOffset = 0

    //  Feed流高度
    private var feedViewPageHeight = 0
    private fun homeIconEvent() {
        mFluentScrollView?.viewTreeObserver?.addOnGlobalLayoutListener {
            if (mFluentScrollView?.height ?: 0 > 0) {
                viewHeight = mFluentScrollView!!.height
            }
        }
        appBarLayout?.addOnOffsetChangedListener { _, verticalOffset ->
            this.verticalOffset = abs(verticalOffset)
            if (viewHeight - this.verticalOffset < 8) {
                playTabIconAnimation(true)
            } else {
                playTabIconAnimation(false)
            }
            if (feedViewPageHeight == 0) {
                feedViewPageHeight = mFeedViewPager?.height ?: 0
            }
            if (feedViewPageHeight == 0) {
                feedViewPageHeight = mFeedViewPager?.height ?: 0
            }
            if (viewHeight != 0) {
                (feedFragmentList?.getOrNull(0) as? FeedFragment)?.let { feedFragment ->
                    if (viewHeight + verticalOffset == 0) {
                        //   Feed流吸顶
                        feedFragment.homeStickyHeaderListener(true)
                    } else {
                        //   Feed流未吸顶
                        feedFragment.homeStickyHeaderListener(false)
                    }
                }
            }

            // 检查吸底Tab的显示状态
            // 添加 isHandlingTabClick 检查，防止点击处理期间的干扰
            if (!isAnimating && isInitialized && !isHandlingTabClick) {
                checkStickyBottomTabVisibility()
            }
        }
    }

    /**
     * playTabIconAnimation
     *
     * @param topIcon topIcon
     */
    private fun playTabIconAnimation(topIcon: Boolean) {
        if (isTopIcon != topIcon) {
            isTopIcon = topIcon
            animator = ValueAnimator.ofFloat(0f, 1f)
            animator?.addUpdateListener {
                val scale = it.animatedValue as Float
                val scaleDiff = 1f - scale
                homeCheckBoxBg?.alpha = if (isTopIcon) {
                    scale
                } else {
                    scaleDiff
                }
                homeCheckBox?.alpha = if (isTopIcon) {
                    scaleDiff
                } else {
                    scale
                }
                homeCheckBox?.scaleX = if (isTopIcon) {
                    scaleDiff
                } else {
                    scale
                }
                homeCheckBox?.scaleY = if (isTopIcon) {
                    scaleDiff
                } else {
                    scale
                }
                homeCheckBoxIcon?.alpha = if (isTopIcon) {
                    scale
                } else {
                    scaleDiff
                }
                homeCheckBoxIcon?.translationY = if (isTopIcon) {
                    scaleDiff
                } else {
                    scale
                } * (homeCheckBoxIcon?.height ?: 1)
            }
            animator?.duration = 300
            animator?.start()
        }
    }

    /**
     * when the user logout, change userInfo view
     * @param event Logout event
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    @Keep
    fun onEvent(event: LogOutEvent?) {
        mIsGetProToken = false
        if (context == null || (context is FragmentActivity && (context as FragmentActivity).isFinishing)) {
            // is nothing todp is
            return
        }

        HomeEngineEvent.sendLoginStatus(mEdgeEngine)
        presenter.getTransferAmountInfo()
        HomeEngineCore.refreshHomeView(mEdgeEngine, requireActivity())
    }

    /**
     * 登录成功  generateUserSig接口不会触发ticket换token，所以此处添加了token的监听来请求generateUserSig接口
     *
     * @param event ProTokenUpdate
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    @Keep
    fun onProTokenUpdate(event: ProTokenUpdate) {
        val proToken = event.proToken
        Log.e("Home", "onProTokenUpdate proToken:$proToken getUI():$ui")
        if (!TextUtils.isEmpty(proToken) && ui != null) {
            presenter.getTransferAmountInfo()
        }
        HomeEngineEvent.sendLoginStatus(mEdgeEngine)
        HomeEngineEvent.sendUnreadMessage(mEdgeEngine)
        mIsGetProToken = true;
    }

    override fun refreshHomeData() {
        if (context == null || (context is FragmentActivity && (context as FragmentActivity).isFinishing)) {
            // is nothing todo it
            return
        }

        HomeEngineCore.refreshHomeView(mEdgeEngine, requireActivity())
    }

    override fun getWebviewContainer(): ViewGroup? {
        return mWebviewPoolContainer
    }

    override fun isGetProToken(): Boolean {
        return mIsGetProToken
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (resultCode == Activity.RESULT_OK && requestCode == 1001) {
            val resultString = data?.getStringExtra(CaptureActivity.RESULT_STRING)
            DebugLog.i("onActivityResult resultString=$resultString")
            if (ScanUtils.isScanLogin(resultString)) {
                UserCenterActionHelper.getInstance().checkLogin(activity, AuthTarget())
            } else if (ScanUtils.isScanFace(resultString)) {
                //安全扫码
                val uri = Uri.parse(resultString)
                val tsvToken = uri.getQueryParameter("tsvToken")
                DebugLog.i(SecuritySetActivity.TAG_QR_SCAN, "tsvToken=$tsvToken")
                if (checkSign(uri)) {
                    if (tsvToken != null) {
                        requestTsvMsg(tsvToken)
                    } else {
                        requestTsvMsg("")
                    }
                } else {
                    DebugLog.e(SecuritySetActivity.TAG_QR_SCAN, "checkSign Failed.")
                    HuobiToastUtil.showErrorShort(R.string.n_qr_scan_not_available)
                }
            } else if (ScanUtils.isScanOpen(resultString)) {
                try {
                    val redPacketResult = RedPacketManager.isRedPacket(resultString)
                    if (redPacketResult.isRedPacket) {
                        val codeWord = redPacketResult.codeWord
                        val aty = activity
                        if (null != aty) {
                            BaseModuleConfig.getCallback().checkLoginByRedPacket(aty, codeWord)
                        }
                    } else {
                        JumpUtils.getInstance().tryPushJumpUrl(Uri.parse(resultString)).checkIt().consumeJumpUrl()
                    }
                } catch (e: java.lang.Exception) {
                    e.printStackTrace()
                    HuobiToastUtil.showErrorShort(R.string.n_qr_scan_not_available)
                }
            } else if (!TextUtils.isEmpty(resultString) && resultString!!.startsWith("file:///android_asset/")) {
                HBBaseWebActivity.showWebView(activity, resultString, null, null, false)
            } else {
                //扫描内容无效提示
                HuobiToastUtil.showErrorShort(R.string.n_qr_scan_not_available)
            }
        }
    }

    /**
     * 检查签名
     *
     * @param uri 扫码得到的uri
     * @return 签名通过
     */
    private fun checkSign(uri: Uri): Boolean {
        val ts = uri.getQueryParameter("ts")
        val sign = uri.getQueryParameter("sign")
        val localSign = MD5Utils.getMD5((ts + "Verify").toByteArray())
        return sign != null && sign.equals(localSign, ignoreCase = true)
    }

    /**
     * 请求提示语
     *
     * @param tsvToken 上个页面扫码获取的token
     */
    fun requestTsvMsg(tsvToken: String) {
        DebugLog.i(SecuritySetActivity.TAG_QR_SCAN, "requestTsvMsg")
        val param = HashMap<String, Any>()
        param["tsvToken"] = tsvToken
        param["uid"] = UserProvider.getInstance().currentId
        param["lang"] = AppLanguageHelper.getInstance().curLanguageHeader
        HRetrofit.risk<RiskService>(RiskService::class.java)
            .getTsvMsg(param)
            .compose<TsvMsg>(HRetrofit.riskIntCodeTransformer<TsvMsg>())
            .compose<TsvMsg>(RxJavaHelper.observeOnMainThread<TsvMsg>(ui))
            .subscribe(object : EasySubscriber<TsvMsg>() {
                override fun onStart() {
                    super.onStart()
                    <EMAIL>()
                }

                override fun onNext(tsvMsg: TsvMsg) {
                    super.onNext(tsvMsg)
                    HUIHandler.getInstance().postDelayed({ VerificationStartActivity.start(activity, tsvToken, tsvMsg.tsvMsg) }, 20)
                }

                override fun onAfter() {
                    super.onAfter()
                    <EMAIL>()
                }
            })
    }

    /**
     * 初始化 wifi 信息 用于安全信息采集等 Android 9.0 需求权限
     *
     * @param activity 上下文
     */
    fun initWifiInfo(activity: Activity?) {
        if (PhoneUtils.isCurVersionAboveAndroidP()) {
            val perms = arrayOf(Manifest.permission.ACCESS_FINE_LOCATION)
            if (EasyPermissions.hasPermissions(context, *perms)) {
                PhoneUtils.getWifiSsid(activity)
                PhoneUtils.getMacAddress(activity)
                PhoneUtils.getNetworkType(activity)
            } else {
                EasyPermissions.requestPermissions(this, ConfigConstant.RC_FINE_LOCATION, *perms)
            }
        } else {
            PhoneUtils.getWifiSsid(activity)
            PhoneUtils.getMacAddress(activity)
            PhoneUtils.getNetworkType(activity)
        }
    }

    override fun onRequestPermissionsResult(requestCode: Int, permissions: Array<out String>, grantResults: IntArray) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        EasyPermissions.onRequestPermissionsResult(requestCode, permissions, grantResults, this)
    }

    override fun onPermissionsGranted(requestCode: Int, perms: List<String?>?) {
        if (ConfigConstant.RC_FINE_LOCATION == requestCode) {
            PhoneUtils.getWifiSsid(activity)
            PhoneUtils.getMacAddress(activity)
            PhoneUtils.getNetworkType(activity)
        }
    }

    override fun onPermissionsDenied(requestCode: Int, perms: List<String?>) {
        DebugLog.i("onPermissionsGranted requestCode=$requestCode perms=$perms")
    }

    /**---------------------------feed--------------------------------**/
    private fun initViewPager() {
        coIndicator = viewFinder.find(R.id.coIndicator)
        mFeedTabLayoutGroup = viewFinder.find(R.id.home_feed_linear_tabLayout)
        mFeedViewPager = viewFinder.find(R.id.home_viewPager)
//        mFeedViewPager?.isSaveEnabled = false
        feedFragmentList = arrayListOf(
            FeedFragment.newInstance(FeedFragment.TAB_TYPE_RECOMMEND),
            FeedFragment.newInstance(FeedFragment.TAB_TYPE_FOLLOW),
            NewsChildFragment.getInstance(-100, "7*24"),
            LiveSquareHomeFragment.newInstance(-100, ""),
            AnnouncementFragment.newInstance()
        )
        tabs = arrayListOf(
            TabData(resources.getString(R.string.n_content_found), 0, 0),
            TabData(resources.getString(R.string.n_content_communityList_attention), 1, 1),
            TabData(resources.getString(R.string.n_content_newsflash), 2, 2),
            TabData(resources.getString(R.string.n_live), 3, 3),
            TabData(resources.getString(R.string.n_notice), 4, 4)
        )
        val adapter = BasePageAdapter(this)
        adapter.addDatas(feedFragmentList!!)
        mFeedViewPager?.adapter = adapter
        mFeedViewPager?.offscreenPageLimit = 1
        activity?.let {
            IndicatorHelper.initBottomNoLineIndicator(
                it,
                tabs!!,
                coIndicator!!,
                0f,
                mFeedViewPager!!,
                16f,
                R.attr.Text_L1,
                R.attr.Text_L3,
                scaleSize = 20f,
                isBold = true,
                onTabClick = object : TabClickListener {
                    override fun onTabClick(index: Int) {
                        if (nowCurrentContentPos != -1 && nowCurrentContentPos == index) {
                            refreshFeedData(ACTION_REFRESH)
                        }
                        nowCurrentContentPos = index
                    }
                }
            )
            coIndicator?.listener = object : CoIndicator.PageSelectListener {
                override fun onSelected(position: Int) {
                    val item = tabs?.get(position)
                    if (0 == item?.type) {
                        SensorsDataHelper.track("app_community_homepage_find_tab", hashMapOf<String, String>())
                    } else if (2 == item?.type) {
                        SensorsDataHelper.newTrack("app_community_kxtab_click", hashMapOf<String, String>())
                    } else if (3 == item?.type) {
                        SensorsDataHelper.newTrack(LiveTrackUtils.LIVE_SQUARE_SHOW)
                    } else if (4 == item?.type) {
                        SensorsDataHelper.newTrack("app_bulletin_tab_click", hashMapOf<String, String>())
                        changeRedPoint(4, false)
                    }

                    // 同步吸底Tab的选中状态
                    syncStickyTabSelection(position)
                }
            }
        }

        with(LiveKeyCons.HOME_FEED_REFRESH, Int::class.java).observe(this) { refreshFeedData(ACTION_REFRESH) }
//        try {
//            coIndicator?.run {
//                setSelectedTabIndicator(0)
//                for (i in 0..tabCount) {
//                    val tab = LayoutInflater.from(context).inflate(R.layout.home_new_content_tab, null)
//                    val title = tab.findViewById<TextView>(R.id.tvTabTitle)
//                    title.setTextSize(TypedValue.COMPLEX_UNIT_PX, 14 * ViewUtils.getScreenWidth(context).toFloat() / 375.0f)
//                    title.text = resources.getString(
//                        when (i) {
//                            0 -> {
//                                title.setTextColor(resources.getColor(R.color.baseColorPrimaryText))
//                                title.setTextSize(TypedValue.COMPLEX_UNIT_PX, 20 * ViewUtils.getScreenWidth(context).toFloat() / 375.0f)
//                                R.string.n_home_feed_tab_recommend
//                            }
//                            1 -> {
//                                R.string.n_content_communityList_attention
//                            }
//                            2 -> {
//                                R.string.n_home_feed_tab_news
//                            }
//                            3 -> {
//                                R.string.n_home_feed_tab_hot
//                            }
//                            4 -> {
//                                R.string.n_content_deep_news
//                            }
//                            else -> {
//                                R.string.n_live
//                            }
//                        }
//                    )
//                    getTabAt(i)?.customView = tab
//                }
//            }
//            coIndicator?.addOnTabSelectedListener(object : OnTabSelectedListener {
//                override fun onTabSelected(tab: TabLayout.Tab) {
//                    try {
//                        tab.customView?.let {
//                            val title = it.findViewById<TextView>(R.id.tvTabTitle)
//                            title.setTextColor(resources.getColor(R.color.baseColorPrimaryText))
//                            title.setTextSize(TypedValue.COMPLEX_UNIT_PX, 20 * ViewUtils.getScreenWidth(context).toFloat() / 375.0f)
//                            if (tab.position == 5) {
//                                SensorsDataHelper.track(
//                                    "app_recome_tab_click",
//                                    HomeSensorsHelper.getFeedShowMap(5)
//                                )
//                            }
//                        }
//                    } catch (e: Exception) {
//                        e.printStackTrace()
//                    }
//                }
//
//                override fun onTabUnselected(tab: TabLayout.Tab) {
//                    try {
//                        tab.customView?.let {
//                            val title = it.findViewById<TextView>(R.id.tvTabTitle)
//                            title.setTextColor(resources.getColor(R.color.baseColorSecondaryTextNew))
//                            title.setTextSize(TypedValue.COMPLEX_UNIT_PX, 14 * ViewUtils.getScreenWidth(context).toFloat() / 375.0f)
//                        }
//                    } catch (e: Exception) {
//                        e.printStackTrace()
//                    }
//                }
//
//                override fun onTabReselected(tab: TabLayout.Tab) {}
//            })
//        } catch (e: IndexOutOfBoundsException) {
//            e.printStackTrace()
//        }
//        HomeHelper.addListener(this, mFluentScrollerLayout, mRefreshLayout, mFeedViewPager, mTabLayout)
        coIndicator?.post { refreshFeedData(IndexInformationRequestData.ACTION_REFRESH) }
        // 增加FeedTablayout Group 事件
        addFeedTabListener()
    }

    private fun addFeedTabListener() {
//        mFluentScrollerLayout?.setOnStickyChangeListener { _: View?, newStickyView: View? ->
//            // Feed TabLayout 实现吸顶
//            if (newStickyView != null && newStickyView.id == mFeedTabLayoutGroup!!.id) {
//                mTabLayout?.setBackgroundResource(R.color.baseColorContentBackground)
//                mFeedTabLayoutGroup!!.setBackgroundResource(R.color.baseColorContentBackground)
//            } else {
//                mTabLayout?.setBackgroundResource(R.color.baseColorContentBackground)
//                mFeedTabLayoutGroup!!.setBackgroundResource(R.color.baseColorContentBackground)
//            }
//        }
    }

    override fun switchTabRefreshData(actionType: Int) {
        super.switchTabRefreshData(actionType)
//        val fragment = HomeHelper.getCurrentFragment(mFeedViewPager, feedFragmentList)
//        if (fragment != null) {
//            fragment.switchTabRefreshData(actionType)
//            return
//        }
//        SensorsDataHelper.track(
//            "app_recome_show",
//            HomeSensorsHelper.getFeedShowMap(
//                HomeHelper.getCurrentTabType(
//                    mFeedViewPager,
//                    feedFragmentList
//                )
//            )
//        )
        setRefreshState()
    }

    override fun refreshFeedPrevPage(position: Int) {
        super.refreshFeedPrevPage(position)
        setRefreshState()
    }

    override fun refreshFeedData(actionType: Int) {
        val fragment = feedFragmentList?.get(mFeedViewPager?.currentItem ?: 0)
        fragment?.let {
            when (it) {
                is FeedFragment -> {
                    it.refreshData(actionType)
                    return
                }

                is NewsChildFragment -> {
                    if (actionType == ACTION_REFRESH) {
                        it.onRefresh(null)
                    }
                }

                is LiveSquareHomeFragment -> {
                    if (actionType == ACTION_REFRESH) {
                        it.refreshPage()
                    }
                }

                else -> {}
            }
        }
        setRefreshState()

    }

    override fun onVisibleChangedFinal(visible: Boolean) {
        super.onVisibleChangedFinal(visible)
        if(visible){
            HomeEngineEvent.sendLoginStatus(mEdgeEngine)
            presenter.getTransferAmountInfo()
            HomeEngineCore.refreshHomeView(mEdgeEngine, requireActivity())
            ProOverviewController.getInstance().subOverview()
        }else{
            ProOverviewController.getInstance().unSubOverViewConditional()
        }
        checkFutureSub()
    }

    @SuppressLint("ClickableViewAccessibility")
    override fun onVisibleChanged(visible: Boolean) {
        super.onVisibleChanged(visible)
        if (visible) {
            HomeEngineEvent.sendCommonConfig(mEdgeEngine)
            HomeEngineEvent.sendRateTypeStr(mEdgeEngine)
            HomeEngineEvent.sendPageAppear(mEdgeEngine)
//            Log.d("home","当前的单位："+HomeEngineEvent.checkPricingMethodChanged())
//            if(HomeEngineEvent.checkPricingMethodChanged()){

//            }
//            presenter.getHomePageConfig()
//            presenter.updateHomeUI()
            homeRadioContainer?.setOnTouchListener { _, _ ->
                if (!feedScroll && verticalOffset >= (mFluentScrollView?.height ?: 1)) {
                    setRefreshState()
                    val behavior = (appBarLayout?.layoutParams as LayoutParams?)?.behavior as AppBarLayout.Behavior?
                    behavior?.setTopAndBottomOffset(0)
                    appBarLayout?.setExpanded(true, true)
                    HUIHandler.getInstance().postDelayed({ refreshFeedData(ACTION_REFRESH) }, 300)
                }
                false
            }
            initRedPointListener()
//            registMarketListener()
            registRankListener()
        } else {
            HomeEngineEvent.sendPageDisappear(mEdgeEngine)
            homeRadioContainer?.setOnTouchListener(null)
//            unRegistMarketListener()
            stopUpdateRankingTimer()
            unregistRankListener()
        }
    }

    /**
     * Tab红点 - 同时更新原生Tab和吸底CoIndicator的红点状态
     * @param index Int
     * @param isShow Boolean
     */
    private fun changeRedPoint(index: Int, isShow: Boolean = false) {
        try {
            // 更新原生Tab的红点
            (coIndicator?.navigator as? CommonNavigator)?.let { commonNavigator ->
                val titleView = commonNavigator.getPagerTitleView(index) as? RedPointPagerTitleView
                if (isShow) {
                    titleView?.showPoint()
                } else {
                    titleView?.hidePoint()
                }
            }

            // 同步更新吸底CoIndicator的红点状态
            updateStickyCoIndicatorRedPoint(index, isShow)

        } catch (e: Throwable) {
            e.printStackTrace()
            Log.d("HomeFragment", "changeRedPoint = $isShow , error = ${e.message ?: ""}")
        }
    }

    class AuthTarget : IAuthTarget, Serializable {
        override fun show(context: Context) {
            Log.d("Home", "---------show--------")
            LoginTokenHelper.getLoginTokenObservable().flatMap { s: String? ->
                HbgGlobalApi.getAPI().scanTokenBind(ScanUtils.getScanLoginCode(), 1, s).observable
            }.compose(RxJavaHelper.observeOnMainThread(null))
                .subscribe(object : EasySubscriber<TokenBindInfo>() {
                    override fun onStart() {
                        super.onStart()
//                        <EMAIL>()
                    }

                    override fun onNext(info: TokenBindInfo) {
                        super.onNext(info)
//                        <EMAIL>()
                        ScanLoginSuccessActivity.startScanLoginSuccessActivity(context, info)
                    }

                    override fun onError2(e: Throwable) {
                        super.onError2(e)
//                        <EMAIL>()
                    }

                    override fun onFailed(e: APIStatusErrorException) {
                        super.onFailed(e)
//                        <EMAIL>()
                    }
                })
        }

        constructor() {
        }

        companion object {
            private const val serialVersionUID = 5620774165925992463L
        }
    }

    // ==================== 吸底Tab功能实现 ====================

    /**
     * 初始化吸底Tab配置
     */
    private fun initStickyBottomTab() {
        // 检测导航栏高度
        detectNavigationBar()

        // 延迟启动滚动监听，确保布局完成
        clLayout?.post {
            clLayout?.postDelayed({
                isInitialized = true  // 标记为已初始化
                Log.d(TAG, "吸底Tab滚动监听已启动")

                // 启动时显示初始吸底Tab（无动画）
                showInitialStickyBottomTab()
            }, 500) // 延迟500ms，确保布局完全完成
        }
    }

    /**
     * 清理吸底Tab资源
     */
    private fun cleanupStickyBottomTab() {
        if (isBottomTabVisible && stickyBottomTabContainer != null && clLayout != null) {
            try {
                clLayout?.removeView(stickyBottomTabContainer)
                stickyBottomTabContainer = null
                stickyCoIndicator = null  // 清理CoIndicator引用
                isBottomTabVisible = false
                isInitialized = false  // 重置初始化标志
            } catch (e: Exception) {
                Log.e(TAG, "清理吸底Tab时发生异常", e)
            }
        }
    }

    /**
     * 检测导航栏高度
     */
    private fun detectNavigationBar() {
        try {
            // 设置底部边距为0，让吸底Tab紧贴屏幕底部（CoordinatorLayout会自动处理导航栏）
            actualBottomMargin = 0
            Log.d(TAG, "导航栏高度设置完成 - 实际底部边距: ${actualBottomMargin}px (紧贴屏幕底部)")
            Log.d(TAG, "CoordinatorLayout将自动处理导航栏适配")
        } catch (e: Exception) {
            Log.w(TAG, "导航栏检测失败，使用默认配置", e)
            actualBottomMargin = 0
        }
    }

    /**
     * 获取导航栏高度
     */
    private fun getNavigationBarHeight(): Int {
        if (!hasNavigationBar()) {
            return 0
        }

        val resourceId = resources.getIdentifier("navigation_bar_height", "dimen", "android")
        return if (resourceId > 0) {
            resources.getDimensionPixelSize(resourceId)
        } else {
            // 默认导航栏高度（约48dp）
            (48 * resources.displayMetrics.density).toInt()
        }
    }

    /**
     * 检查设备是否有导航栏
     */
    private fun hasNavigationBar(): Boolean {
        val activity = activity ?: return false

        return try {
            val windowManager = activity.windowManager
            val display = windowManager.defaultDisplay
            val realDisplayMetrics = android.util.DisplayMetrics()
            val displayMetrics = android.util.DisplayMetrics()

            display.getRealMetrics(realDisplayMetrics)
            display.getMetrics(displayMetrics)

            // 如果真实高度大于显示高度，说明有导航栏
            realDisplayMetrics.heightPixels > displayMetrics.heightPixels
        } catch (e: Exception) {
            Log.w(TAG, "检测导航栏失败", e)
            false
        }
    }

    /**
     * 检查吸底Tab的显示状态 - 非对称切换逻辑，避免重叠显示
     *
     * 切换策略：
     * - 显示吸底Tab：当原生Tab大部分滚出屏幕时（底部超过 screenHeight - tabHeight）
     * - 隐藏吸底Tab：当原生Tab刚开始进入屏幕时（显示约20%时）就立即隐藏
     *
     * 这样可以避免原生Tab和吸底Tab同时显示的重叠情况
     */
    private fun checkStickyBottomTabVisibility() {
        if (isAnimating || coIndicator == null) {
            if (isAnimating) {
                Log.v(TAG, "跳过可见性检查：正在动画中")
            }
            return
        }

        // 如果正在处理Tab点击，跳过检查
        if (isHandlingTabClick) {
            Log.v(TAG, "跳过可见性检查：正在处理Tab点击")
            return
        }

        // 检查CoIndicator是否已经完成布局
        if (coIndicator!!.height <= 0 || coIndicator!!.width <= 0) {
            return
        }

        // 获取CoIndicator的屏幕位置信息
        val location = IntArray(2)
        coIndicator!!.getLocationOnScreen(location)
        val tabScreenY = location[1]
        val tabHeight = coIndicator!!.height
        val tabBottomY = tabScreenY + tabHeight
        val screenHeight = resources.displayMetrics.heightPixels

        // 优化判断逻辑：非对称切换，避免重叠显示
        // 显示阈值：当原生Tab大部分滚出屏幕时显示吸底Tab
        val showThreshold = screenHeight - tabHeight

        // 隐藏阈值：当原生Tab刚开始进入屏幕时就隐藏吸底Tab（提前触发）
        val hideThreshold = screenHeight - (tabHeight * 0.2f).toInt() // 原生Tab显示20%时就隐藏吸底Tab

        // 计算切换状态（非对称逻辑）
        val shouldShowStickyTab = tabBottomY > showThreshold  // 原生Tab底部超过显示阈值时显示吸底Tab
        val shouldHideStickyTab = tabBottomY <= hideThreshold // 原生Tab底部回到隐藏阈值内时隐藏吸底Tab（更早触发）

        // 添加安全检查：确保不是初始状态的异常位置
        val isValidPosition = tabScreenY > -tabHeight && tabScreenY < screenHeight * 2

        // 添加调试日志
        Log.v(TAG, "非对称切换检测 - tabScreenY: $tabScreenY, tabBottomY: $tabBottomY")
        Log.v(TAG, "阈值设置 - showThreshold: $showThreshold, hideThreshold: $hideThreshold")
        Log.v(TAG, "切换状态 - shouldShowStickyTab: $shouldShowStickyTab, shouldHideStickyTab: $shouldHideStickyTab, isBottomTabVisible: $isBottomTabVisible")
        Log.v(TAG, "距离分析 - 原生Tab距离屏幕底部: ${screenHeight - tabBottomY}px, Tab高度: ${tabHeight}px")
        Log.v(TAG, "可见度分析 - 原生Tab可见高度: ${maxOf(0, screenHeight - tabScreenY)}px (${(maxOf(0, screenHeight - tabScreenY).toFloat() / tabHeight * 100).toInt()}%)")

        // 获取当前时间，用于防抖动
        val currentTime = System.currentTimeMillis()
        val isDebounceTimeElapsed = currentTime - lastSwitchTime > switchDebounceDelay

        when {
            shouldHideStickyTab && isBottomTabVisible && isDebounceTimeElapsed -> {
                // 原生Tab刚开始进入屏幕（显示约20%）时，提前隐藏吸底Tab，避免重叠
                val visiblePercent = (maxOf(0, screenHeight - tabScreenY).toFloat() / tabHeight * 100).toInt()
                Log.d(TAG, "原生Tab进入屏幕(${visiblePercent}%)，提前隐藏吸底Tab，避免重叠显示")
                hideStickyBottomTab()
                lastSwitchTime = currentTime  // 更新切换时间
            }
            shouldShowStickyTab && !isBottomTabVisible && isValidPosition && isDebounceTimeElapsed -> {
                // 原生Tab大部分滚出屏幕时，显示吸底Tab
                Log.d(TAG, "原生Tab大部分滚出屏幕，显示吸底Tab")
                showStickyBottomTab()
                lastSwitchTime = currentTime  // 更新切换时间
            }
        }
    }

    /**
     * 启动时显示初始吸底Tab（无动画）
     */
    private fun showInitialStickyBottomTab() {
        clLayout?.post {
            clLayout?.postDelayed({
                if (!isAnimating && !isBottomTabVisible) {
                    Log.d(TAG, "显示初始吸底Tab（无动画）")
                    showStickyBottomTabWithoutAnimation()
                }
            }, 100) // 短暂延迟确保布局完成
        }
    }

    /**
     * 显示吸底Tab（无动画，用于初始显示）
     */
    private fun showStickyBottomTabWithoutAnimation() {
        if (isAnimating || isBottomTabVisible || stickyBottomTabContainer != null || clLayout == null) return

        Log.d(TAG, "开始创建初始吸底Tab（无动画）")
        isBottomTabVisible = true

        // 创建吸底Tab容器和CoIndicator
        stickyBottomTabContainer = createStickyTabContainer()
        stickyCoIndicator = createStickyCoIndicator()
        stickyBottomTabContainer?.addView(stickyCoIndicator)

        // 添加到CoordinatorLayout底部，考虑导航栏适配
        val layoutParams = CoordinatorLayout.LayoutParams(
            CoordinatorLayout.LayoutParams.MATCH_PARENT,
            CoordinatorLayout.LayoutParams.WRAP_CONTENT
        ).apply {
            gravity = Gravity.BOTTOM
            bottomMargin = actualBottomMargin
        }

        clLayout?.addView(stickyBottomTabContainer, layoutParams)

        // 直接显示，无动画，确保位置正确
        stickyBottomTabContainer?.let { container ->
            container.translationY = 0f
            container.alpha = 1f
            container.scaleY = 1f
            Log.d(TAG, "初始吸底Tab显示完成 - 位置: translationY=${container.translationY}, alpha=${container.alpha}, scaleY=${container.scaleY}")
            Log.d(TAG, "容器布局参数 - bottomMargin=$actualBottomMargin")
        }
    }

    /**
     * 显示吸底Tab
     */
    private fun showStickyBottomTab() {
        if (isAnimating || isBottomTabVisible || stickyBottomTabContainer != null || clLayout == null) return

        Log.d(TAG, "开始创建吸底Tab")
        isAnimating = true
        isBottomTabVisible = true

        // 创建吸底Tab容器和CoIndicator
        stickyBottomTabContainer = createStickyTabContainer()
        stickyCoIndicator = createStickyCoIndicator()
        stickyBottomTabContainer?.addView(stickyCoIndicator)

        // 添加到CoordinatorLayout底部，考虑导航栏适配
        val layoutParams = CoordinatorLayout.LayoutParams(
            CoordinatorLayout.LayoutParams.MATCH_PARENT,
            CoordinatorLayout.LayoutParams.WRAP_CONTENT
        ).apply {
            gravity = Gravity.BOTTOM
            bottomMargin = actualBottomMargin
        }

        clLayout?.addView(stickyBottomTabContainer, layoutParams)

        // 优化的入场动画：从底部滑入，使用更柔和的插值器和透明度变化
        stickyBottomTabContainer?.let { container ->
            // 初始状态：位于底部且透明
            container.translationY = container.height.toFloat() * 0.3f  // 减小初始位移，使过渡更自然
            container.alpha = 0f
            container.scaleY = 0.9f  // 轻微缩放效果，减小变化幅度使过渡更自然

            container.animate()
                .translationY(0f) // 确保最终位置是0，紧贴底部
                .alpha(1f)
                .scaleY(1f)
                .setDuration(animationDuration)
                .setInterpolator(AccelerateDecelerateInterpolator()) // 使用不会过冲的插值器
                .setListener(object : AnimatorListenerAdapter() {
                    override fun onAnimationEnd(animation: Animator) {
                        // 确保动画结束后位置完全正确
                        container.translationY = 0f
                        container.alpha = 1f
                        container.scaleY = 1f
                        isAnimating = false
                        Log.d(TAG, "吸底Tab入场动画完成，位置已校正")
                        Log.d(TAG, "最终位置: translationY=${container.translationY}, alpha=${container.alpha}, scaleY=${container.scaleY}")
                        Log.d(TAG, "容器布局参数 - bottomMargin=$actualBottomMargin")
                    }
                    override fun onAnimationCancel(animation: Animator) {
                        // 动画取消时也要确保位置正确
                        container.translationY = 0f
                        container.alpha = 1f
                        container.scaleY = 1f
                        isAnimating = false
                        Log.d(TAG, "吸底Tab入场动画取消，位置已校正")
                    }
                })
                .start()
        }
    }

    /**
     * 隐藏吸底Tab
     */
    private fun hideStickyBottomTab() {
        hideStickyBottomTabWithCallback(null)
    }

    /**
     * 隐藏吸底Tab - 带完成回调，防止异常显示
     */
    private fun hideStickyBottomTabWithCallback(onComplete: (() -> Unit)?) {
        Log.d(TAG, "=== 隐藏吸底Tab开始 ===")
        Log.d(TAG, "当前状态: isAnimating=$isAnimating, isBottomTabVisible=$isBottomTabVisible")
        Log.d(TAG, "容器状态: stickyBottomTabContainer=${stickyBottomTabContainer != null}, clLayout=${clLayout != null}")

        if (isAnimating || !isBottomTabVisible || stickyBottomTabContainer == null || clLayout == null) {
            Log.d(TAG, "无法执行隐藏动画，直接执行回调")
            // 如果无法执行隐藏动画，直接执行回调
            onComplete?.invoke()
            return
        }

        Log.d(TAG, "开始执行隐藏动画")
        isAnimating = true
        isBottomTabVisible = false

        val containerToRemove = stickyBottomTabContainer
        stickyBottomTabContainer = null
        stickyCoIndicator = null  // 清理CoIndicator引用

        // 优化的退场动画：向底部滑出，与入场动画保持一致的自然过渡
        containerToRemove?.animate()
            ?.translationY(containerToRemove.height.toFloat() * 0.3f)  // 与入场动画保持一致的位移距离
            ?.alpha(0f)
            ?.scaleY(0.9f)  // 与入场动画保持一致的缩放效果
            ?.setDuration(animationDuration)
            ?.setInterpolator(AccelerateDecelerateInterpolator()) // 使用平滑的插值器
            ?.setListener(object : AnimatorListenerAdapter() {
                override fun onAnimationEnd(animation: Animator) {
                    try {
                        clLayout?.removeView(containerToRemove)
                        Log.d(TAG, "吸底Tab退场动画完成，容器已移除")
                    } catch (e: Exception) {
                        Log.e(TAG, "移除吸底Tab时发生异常", e)
                    }
                    isAnimating = false

                    // 动画完成后执行回调
                    Log.d(TAG, "执行隐藏完成回调")
                    onComplete?.invoke()
                }
                override fun onAnimationCancel(animation: Animator) {
                    Log.d(TAG, "隐藏动画被取消")
                    isAnimating = false
                    // 动画取消时也执行回调，确保流程继续
                    onComplete?.invoke()
                }
            })
            ?.start()
    }

    /**
     * 创建吸底Tab容器 - 与原生CoIndicator样式一致
     */
    private fun createStickyTabContainer(): LinearLayout {
        return LinearLayout(requireContext()).apply {
            orientation = LinearLayout.HORIZONTAL
            gravity = Gravity.CENTER_VERTICAL
            setBackgroundColor(Color.WHITE)
            elevation = 8f
            // 与原生CoIndicator相同的padding设置
            setPadding(dp2px(8f), 0, dp2px(8f), 0)
        }
    }

    /**
     * dp转px工具方法
     */
    private fun dp2px(dp: Float): Int {
        return (dp * resources.displayMetrics.density + 0.5f).toInt()
    }

    /**
     * 创建吸底CoIndicator - 直接复用原生组件
     */
    private fun createStickyCoIndicator(): CoIndicator {
        Log.d(TAG, "开始创建吸底CoIndicator")

        // 创建新的CoIndicator实例
        val stickyCoIndicator = CoIndicator(requireContext()).apply {
            layoutParams = LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT,
                dp2px(40f) // 与原生CoIndicator相同的高度
            )
        }

        // 使用与原生相同的初始化方法和参数
        activity?.let { activity ->
            tabs?.let { tabList ->
                Log.d(TAG, "使用原生IndicatorHelper初始化吸底CoIndicator")

                // 使用与原生完全相同的配置参数
                IndicatorHelper.initBottomNoLineIndicator(
                    activity,
                    tabList,
                    stickyCoIndicator,
                    0f, // indicatorYOffset
                    null, // 不绑定ViewPager，避免冲突
                    16f, // textSize
                    R.attr.Text_L1, // selColor
                    R.attr.Text_L3, // unSelColor
                    scaleSize = 20f,
                    isBold = true,
                    onTabClick = object : TabClickListener {
                        override fun onTabClick(index: Int) {
                            handleStickyTabClick(index)
                        }
                    }
                )

                // 设置初始选中状态
                val currentPosition = mFeedViewPager?.currentItem ?: 0
                stickyCoIndicator.onPageSelected(currentPosition)
            }
        }

        return stickyCoIndicator
    }

    /**
     * 处理吸底Tab点击事件 - 优化动画执行顺序，防止异常显示
     */
    private fun handleStickyTabClick(position: Int) {
        Log.d(TAG, "=== 吸底Tab点击开始 ===")
        Log.d(TAG, "点击位置: position=$position")
        Log.d(TAG, "当前状态: isBottomTabVisible=$isBottomTabVisible, isAnimating=$isAnimating, isHandlingTabClick=$isHandlingTabClick")

        // 设置点击处理标志，防止滚动监听器干扰
        isHandlingTabClick = true

        // 切换ViewPager页面
        mFeedViewPager?.setCurrentItem(position, true)
        Log.d(TAG, "ViewPager页面已切换")

        // 同步原生CoIndicator的状态
        coIndicator?.onPageSelected(position)
        Log.d(TAG, "原生CoIndicator状态已同步")

        // 执行与原生Tab相同的业务逻辑
        val item = tabs?.get(position)
        if (0 == item?.type) {
            SensorsDataHelper.track("app_community_homepage_find_tab", hashMapOf<String, String>())
        } else if (2 == item?.type) {
            SensorsDataHelper.newTrack("app_community_kxtab_click", hashMapOf<String, String>())
        } else if (3 == item?.type) {
            SensorsDataHelper.newTrack(LiveTrackUtils.LIVE_SQUARE_SHOW)
        } else if (4 == item?.type) {
            SensorsDataHelper.newTrack("app_bulletin_tab_click", hashMapOf<String, String>())
            changeRedPoint(4, false)
        }
        Log.d(TAG, "业务逻辑执行完成")

        // 调整执行顺序：先隐藏吸底Tab，再执行滚动动画
        Log.d(TAG, "开始隐藏吸底Tab，然后执行滚动动画")
        hideStickyBottomTabWithCallback {
            // 吸底Tab隐藏动画完成后，再执行滚动动画
            Log.d(TAG, "吸底Tab隐藏完成，开始滚动到原始Tab位置")
            performScrollToPosition()
        }
    }







    /**
     * 获取主题属性颜色
     */
    private fun getAttrColor(attrRes: Int): Int {
        val typedValue = android.util.TypedValue()
        requireContext().theme.resolveAttribute(attrRes, typedValue, true)
        return ContextCompat.getColor(requireContext(), typedValue.resourceId)
    }



    /**
     * 滚动到指定位置（默认屏幕1/3处）- 防止异常显示
     */
    private fun performScrollToPosition() {
        Log.d(TAG, "=== 滚动到指定位置开始 ===")
        Log.d(TAG, "当前状态: isAnimating=$isAnimating, isHandlingTabClick=$isHandlingTabClick")

        // 如果正在动画中，不执行滚动
        if (isAnimating) {
            Log.d(TAG, "正在动画中，跳过滚动")
            return
        }

        clLayout?.post {
            val screenHeight = resources.displayMetrics.heightPixels
            val targetY = (screenHeight * scrollToPosition).toInt()

            // 先完全展开AppBarLayout
            appBarLayout?.setExpanded(true, false)

            // 等待布局完成后计算需要的滚动距离
            clLayout?.post {
                val tabLocation = IntArray(2)
                coIndicator?.getLocationOnScreen(tabLocation)
                val currentTabY = tabLocation[1]

                // 计算需要向下滚动的距离
                val scrollDistance = currentTabY - targetY

                if (scrollDistance > 0) {
                    // 设置动画标志，防止滚动监听器干扰
                    isAnimating = true

                    // 使用AppBarLayout的Behavior来精确控制滚动
                    val behavior = (appBarLayout?.layoutParams as? CoordinatorLayout.LayoutParams)?.behavior
                    if (behavior is AppBarLayout.Behavior) {
                        val totalScrollRange = appBarLayout?.totalScrollRange ?: 0
                        val targetOffset = kotlin.math.min(totalScrollRange, scrollDistance)

                        // 使用动画平滑滚动到目标位置
                        val animator = ValueAnimator.ofInt(0, targetOffset)
                        animator.duration = 400
                        animator.interpolator = DecelerateInterpolator()
                        animator.addUpdateListener { animation ->
                            val offset = animation.animatedValue as Int
                            behavior.topAndBottomOffset = -offset
                            appBarLayout?.requestLayout()
                        }

                        // 添加动画结束监听器
                        animator.addListener(object : AnimatorListenerAdapter() {
                            override fun onAnimationEnd(animation: Animator) {
                                super.onAnimationEnd(animation)
                                isAnimating = false // 重置动画标志
                                isHandlingTabClick = false // 重置点击处理标志，恢复滚动监听
                                Log.d(TAG, "滚动动画完成，状态已重置")
                                Log.d(TAG, "=== 吸底Tab点击处理完成 ===")
                            }

                            override fun onAnimationCancel(animation: Animator) {
                                super.onAnimationCancel(animation)
                                isAnimating = false // 重置动画标志
                                isHandlingTabClick = false // 重置点击处理标志，恢复滚动监听
                                Log.d(TAG, "滚动动画被取消，状态已重置")
                            }
                        })

                        animator.start()
                        Log.d(TAG, "开始平滑滚动到目标位置")
                    } else {
                        isAnimating = false // 如果无法获取behavior，重置标志
                    }
                } else {
                    // 如果不需要滚动，重置状态（吸底Tab已在点击时隐藏）
                    isHandlingTabClick = false // 重置点击处理标志，恢复滚动监听
                    Log.d(TAG, "无需滚动，状态已重置")
                    Log.d(TAG, "=== 吸底Tab点击处理完成 ===")
                }

                Log.d(TAG, "滚动到指定位置处理完成")
            }
        }
    }

    /**
     * 设置选中的Tab - 与原生CoIndicator样式一致，支持红点功能
     */
    private fun setSelectedTab(container: ViewGroup, position: Int) {
        try {
            if (container is LinearLayout) {
                // 获取原生的颜色属性
                val unselectedColor = try {
                    getAttrColor(R.attr.Text_L3) // 未选中颜色
                } catch (e: Exception) {
                    Color.GRAY
                }

                val selectedColor = try {
                    getAttrColor(R.attr.Text_L1) // 选中颜色
                } catch (e: Exception) {
                    Color.BLACK
                }

                // 重置所有Tab状态
                for (i in 0 until container.childCount) {
                    val tabContainer = container.getChildAt(i) as? RelativeLayout
                    val textView = tabContainer?.getChildAt(0) as? TextView // 第一个子视图是TextView
                    textView?.let { tv ->
                        tv.setTextColor(unselectedColor)
                        // 重置字体大小为普通大小
                        tv.textSize = 16f
                    }
                }

                // 设置选中Tab状态
                if (position in 0 until container.childCount) {
                    val selectedTabContainer = container.getChildAt(position) as? RelativeLayout
                    val selectedTextView = selectedTabContainer?.getChildAt(0) as? TextView
                    selectedTextView?.let { tv ->
                        tv.setTextColor(selectedColor)
                        // 设置选中状态的字体大小（与原生scaleSize = 20f一致）
                        tv.textSize = 20f
                    }
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "设置选中Tab失败", e)
        }
    }

    /**
     * 同步吸底CoIndicator的选中状态
     */
    private fun syncStickyTabSelection(position: Int) {
        if (isBottomTabVisible && stickyCoIndicator != null) {
            // 直接调用CoIndicator的onPageSelected方法同步状态
            stickyCoIndicator?.onPageSelected(position)
            Log.d(TAG, "已同步吸底CoIndicator选中状态: position=$position")
        }
    }

    /**
     * 更新吸底CoIndicator的红点状态
     */
    private fun updateStickyCoIndicatorRedPoint(position: Int, show: Boolean) {
        try {
            // 如果吸底CoIndicator正在显示，同步更新红点
            if (isBottomTabVisible && stickyCoIndicator != null) {
                (stickyCoIndicator?.navigator as? CommonNavigator)?.let { commonNavigator ->
                    val titleView = commonNavigator.getPagerTitleView(position) as? RedPointPagerTitleView
                    if (show) {
                        titleView?.showPoint()
                    } else {
                        titleView?.hidePoint()
                    }
                    Log.d(TAG, "已同步吸底CoIndicator红点状态: position=$position, show=$show")
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "同步吸底CoIndicator红点状态失败", e)
        }
    }

    /**
     * 公开方法：设置Tab红点状态（供外部调用）
     */
    fun setStickyTabRedPoint(position: Int, show: Boolean) {
        updateStickyCoIndicatorRedPoint(position, show)
    }

}