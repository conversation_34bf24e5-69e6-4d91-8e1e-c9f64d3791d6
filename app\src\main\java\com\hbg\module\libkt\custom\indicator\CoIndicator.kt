package com.hbg.module.libkt.custom.indicator

import android.content.Context
import android.util.AttributeSet
import android.widget.FrameLayout
import com.google.android.material.tabs.TabLayout

/**
 * CoIndicator - 技术栈一致：继承自FrameLayout，内部使用TabLayout
 */
class CoIndicator : FrameLayout {

    var listener: PageSelectListener? = null
    private val internalTabLayout: TabLayout

    constructor(context: Context) : super(context) {
        internalTabLayout = TabLayout(context)
        init()
    }

    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs) {
        internalTabLayout = TabLayout(context, attrs)
        init()
    }

    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(context, attrs, defStyleAttr) {
        internalTabLayout = TabLayout(context, attrs, defStyleAttr)
        init()
    }

    private fun init() {
        // 将内部TabLayout添加到FrameLayout中
        addView(internalTabLayout, LayoutParams(LayoutParams.MATCH_PARENT, LayoutParams.MATCH_PARENT))

        // 设置Tab选择监听器
        internalTabLayout.addOnTabSelectedListener(object : TabLayout.OnTabSelectedListener {
            override fun onTabSelected(tab: TabLayout.Tab?) {
                tab?.position?.let { position ->
                    listener?.onSelected(position)
                }
            }

            override fun onTabUnselected(tab: TabLayout.Tab?) {
                // 可以在这里处理取消选中的逻辑
            }

            override fun onTabReselected(tab: TabLayout.Tab?) {
                tab?.position?.let { position ->
                    // 重复点击同一Tab，可以触发刷新
                    android.util.Log.d("CoIndicator", "重复点击Tab: $position")
                    listener?.onSelected(position)
                }
            }
        })
    }

    /**
     * ViewPager滚动时调用（兼容客户代码的方法签名）
     */
    fun onPageScrolled(position: Int, positionOffset: Float, positionOffsetPixels: Int) {
        // 委托给内部TabLayout
    }

    /**
     * ViewPager页面选中时调用（兼容客户代码的方法签名）
     */
    fun onPageSelected(position: Int) {
        // 选中对应的Tab
        if (position >= 0 && position < internalTabLayout.tabCount) {
            internalTabLayout.getTabAt(position)?.select()
        }
    }

    /**
     * ViewPager滚动状态改变时调用（兼容客户代码的方法签名）
     */
    fun onPageScrollStateChanged(state: Int) {
        // 委托给内部TabLayout
    }

    /**
     * 设置页面选择监听器（兼容客户代码的方法签名）
     */
    fun setOnPageSelectListener(listener: PageSelectListener) {
        this.listener = listener
    }

    /**
     * 页面选择监听器接口（兼容客户代码）
     */
    interface PageSelectListener {
        fun onSelected(position: Int)
    }

    /**
     * 获取指定位置的Tab视图（插件需要此方法）
     */
    fun getPagerTitleView(index: Int): TabLayout.Tab? {
        return internalTabLayout.getTabAt(index)
    }

    /**
     * 显示红点（在指定Tab上显示红点）
     */
    fun showRedPoint(position: Int) {
        internalTabLayout.getTabAt(position)?.let { tab ->
            val originalText = tab.text?.toString() ?: ""
            if (!originalText.contains("●")) {
                tab.text = "$originalText ●"
            }
        }
    }

    /**
     * 隐藏红点（移除指定Tab上的红点）
     */
    fun hideRedPoint(position: Int) {
        internalTabLayout.getTabAt(position)?.let { tab ->
            val originalText = tab.text?.toString()?.replace(" ●", "") ?: ""
            tab.text = originalText
        }
    }

    /**
     * 获取Tab数量（插件需要此方法）
     */
    val tabCount: Int
        get() = internalTabLayout.tabCount

    /**
     * 获取内部TabLayout（用于TabLayoutMediator）
     */
    val tabLayout: TabLayout
        get() = internalTabLayout
}
