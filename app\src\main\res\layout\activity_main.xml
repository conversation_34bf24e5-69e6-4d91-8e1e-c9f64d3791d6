<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout 
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/coordinator_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".MainActivity">

    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/app_bar_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar">

        <!-- 工具栏 -->
        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="?attr/colorPrimary"
            app:layout_scrollFlags="scroll|enterAlways"
            app:popupTheme="@style/ThemeOverlay.AppCompat.Light"
            app:title="吸底Tab插件演示" />

        <!-- 头部内容区域 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="600dp"
            android:background="@color/design_default_color_primary"
            android:gravity="center"
            android:orientation="vertical"
            app:layout_scrollFlags="scroll|enterAlways">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="吸底Tab插件演示"
                android:textColor="@android:color/white"
                android:textSize="24sp"
                android:textStyle="bold" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:text="这是一个很长的头部内容区域"
                android:textColor="@android:color/white"
                android:textSize="18sp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:text="向下滚动查看吸底Tab效果"
                android:textColor="@android:color/white"
                android:textSize="16sp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="32dp"
                android:text="功能特性："
                android:textColor="@android:color/white"
                android:textSize="16sp"
                android:textStyle="bold" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:text="• 智能吸底显示"
                android:textColor="@android:color/white"
                android:textSize="14sp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:text="• 流畅动画效果"
                android:textColor="@android:color/white"
                android:textSize="14sp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:text="• 一键返回原位"
                android:textColor="@android:color/white"
                android:textSize="14sp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:text="• 状态完全同步"
                android:textColor="@android:color/white"
                android:textSize="14sp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="32dp"
                android:text="继续向下滚动..."
                android:textColor="@android:color/white"
                android:textSize="14sp"
                android:alpha="0.8" />

        </LinearLayout>

        <!-- 额外的头部内容区域 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="300dp"
            android:background="#E3F2FD"
            android:gravity="center"
            android:orientation="vertical"
            app:layout_scrollFlags="scroll|enterAlways">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="更多内容区域"
                android:textColor="@color/design_default_color_primary"
                android:textSize="20sp"
                android:textStyle="bold" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:text="这里是额外的内容区域"
                android:textColor="@color/design_default_color_primary"
                android:textSize="16sp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:text="继续滚动直到Tab消失"
                android:textColor="@color/design_default_color_primary"
                android:textSize="14sp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="32dp"
                android:text="当Tab滚出屏幕时"
                android:textColor="@color/design_default_color_primary"
                android:textSize="14sp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:text="吸底Tab会自动显示"
                android:textColor="@color/design_default_color_primary"
                android:textSize="14sp"
                android:textStyle="bold" />

        </LinearLayout>

        <!-- Tab布局 - 基于TabLayout的CoIndicator -->
        <com.hbg.module.libkt.custom.indicator.CoIndicator
            android:id="@+id/co_indicator"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@android:color/white"
            app:tabIndicatorColor="@color/design_default_color_primary"
            app:tabSelectedTextColor="@color/design_default_color_primary"
            app:tabTextColor="@android:color/black"
            app:tabMode="scrollable"
            app:tabGravity="start" />

    </com.google.android.material.appbar.AppBarLayout>

    <!-- ViewPager内容区域 -->
    <androidx.viewpager2.widget.ViewPager2
        android:id="@+id/view_pager"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>
