package com.ttv.demoapp

import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import androidx.coordinatorlayout.widget.CoordinatorLayout
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import androidx.viewpager2.adapter.FragmentStateAdapter
import androidx.viewpager2.widget.ViewPager2
import com.google.android.material.appbar.AppBarLayout

import com.hbg.module.libkt.custom.indicator.CoIndicator
import com.hbg.module.libkt.custom.indicator.TabData
import com.google.android.material.tabs.TabLayout
import com.google.android.material.tabs.TabLayoutMediator
import com.ttv.demoapp.pugin.StickyBottomTabPlugin
import com.ttv.demoapp.pugin.StickyBottomTabPluginFactory

/**
 * 吸底Tab插件演示Activity
 * 展示插件的基本功能和使用方法
 */
class MainActivity : AppCompatActivity() {
    
    private lateinit var coordinatorLayout: CoordinatorLayout
    private lateinit var appBarLayout: AppBarLayout
    private lateinit var coIndicator: CoIndicator
    private lateinit var viewPager: ViewPager2
    
    // 吸底Tab插件实例
    private var stickyBottomTabPlugin: StickyBottomTabPlugin? = null
    
    // Tab数据（使用客户的数据结构）
    private val tabs = arrayListOf(
        TabData("首页", 0, 0),
        TabData("发现", 1, 1),
        TabData("消息", 2, 2),
        TabData("我的", 3, 3),
        TabData("设置", 4, 4)
    )

    // Tab标题（从TabData提取）
    private val tabTitles = tabs.map { it.name ?: "" }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_main)

        // 完全隐藏ActionBar
        supportActionBar?.hide()

        initViews()
        setupViewPager()
        setupCoIndicator()
        setupStickyBottomTabPlugin()
    }
    
    /**
     * 初始化视图组件
     */
    private fun initViews() {
        coordinatorLayout = findViewById<CoordinatorLayout>(R.id.coordinator_layout)
        appBarLayout = findViewById<AppBarLayout>(R.id.app_bar_layout)
        coIndicator = findViewById<CoIndicator>(R.id.co_indicator)
        viewPager = findViewById<ViewPager2>(R.id.view_pager)
    }
    
    /**
     * 设置ViewPager和CoIndicator
     */
    private fun setupViewPager() {
        // 创建ViewPager适配器
        val adapter = DemoFragmentAdapter(this, tabTitles)
        viewPager.adapter = adapter

        android.util.Log.d("MainActivity", "ViewPager设置完成，Tab数量: ${tabTitles.size}")
    }

    /**
     * 设置CoIndicator（FrameLayout包装TabLayout的实现）
     */
    private fun setupCoIndicator() {
        android.util.Log.d("MainActivity", "开始设置CoIndicator（FrameLayout基础，内部TabLayout）...")

        // 使用TabLayoutMediator连接CoIndicator内部的TabLayout和ViewPager
        TabLayoutMediator(coIndicator.tabLayout, viewPager) { tab, position ->
            val tabData = tabs[position]
            tab.text = tabData.name
        }.attach()

        // 设置Tab点击监听器（用于重复点击检测）
        coIndicator.setOnPageSelectListener(object : CoIndicator.PageSelectListener {
            override fun onSelected(position: Int) {
                android.util.Log.d("MainActivity", "CoIndicator选中Tab: $position")
                // 这里可以处理重复点击刷新逻辑
            }
        })

        android.util.Log.d("MainActivity", "✓ CoIndicator设置完成（FrameLayout基础）")

        // 测试红点功能
        testRedPointFeature()
    }

    /**
     * 测试红点功能（TabLayout基础）
     */
    private fun testRedPointFeature() {
        // 延迟3秒显示红点，用于测试
        coIndicator.postDelayed({
            coIndicator.showRedPoint(2) // 在"消息"Tab显示红点
            coIndicator.showRedPoint(3) // 在"我的"Tab显示红点
            android.util.Log.d("MainActivity", "✓ 红点测试：已在Tab 2和3显示红点（TabLayout基础）")
        }, 3000)
    }

    /**
     * 设置吸底Tab插件（简化版）
     */
    private fun setupStickyBottomTabPlugin() {
        try {
            android.util.Log.d("MainActivity", "正在创建吸底Tab插件...")

            // 模拟红点状态（实际使用时从业务逻辑获取）
            val redPointStates = mapOf(
                3 to true,  // 第4个Tab有红点
                4 to true   // 第5个Tab有红点
            )

            // 使用简化的工厂方法创建插件
            stickyBottomTabPlugin = StickyBottomTabPluginFactory.createForCoIndicator(
                context = this,
                coordinatorLayout = coordinatorLayout,
                appBarLayout = appBarLayout,
                coIndicator = coIndicator,
                viewPager = viewPager,
                tabTitles = tabTitles,           // 直接传参数！
                selectedPosition = 0,            // 当前选中位置
                redPointStates = redPointStates, // 红点状态
                animationDuration = 300L,        // 动画时长
                scrollToPosition = 0.33f,        // 点击后滚动到屏幕1/3位置
                autoDetectNavigationBar = true,  // 启用导航栏自动检测
                bottomMargin = 0,                // 底部边距
                horizontalMargin = 0             // 水平边距
            )

            // 启用插件
            stickyBottomTabPlugin?.enable()

            android.util.Log.d("MainActivity", "✓ 吸底Tab插件创建并启用成功")
            android.util.Log.d("MainActivity", "✓ 使用直接参数传递，无需复杂适配器")

            // 验证插件兼容性
            verifyPluginCompatibility()

        } catch (e: Exception) {
            android.util.Log.e("MainActivity", "创建吸底Tab插件失败", e)
        }
    }

    /**
     * 验证插件兼容性
     */
    private fun verifyPluginCompatibility() {
        android.util.Log.d("MainActivity", "开始验证插件兼容性...")

        try {
            // 验证CoIndicator类型
            val coIndicatorClass = coIndicator::class.java
            android.util.Log.d("MainActivity", "✓ CoIndicator类型: ${coIndicatorClass.name}")
            android.util.Log.d("MainActivity", "✓ 继承自FrameLayout: ${coIndicatorClass.superclass?.name}")

            // 验证Tab数量
            val tabCount = coIndicator.tabCount
            android.util.Log.d("MainActivity", "✓ Tab数量: $tabCount")

            // 验证Tab内容
            for (i in 0 until tabCount) {
                val tab = coIndicator.getPagerTitleView(i)
                android.util.Log.d("MainActivity", "✓ Tab $i: ${tab?.text}")
            }

            // 验证红点功能
            val titleView = coIndicator.getPagerTitleView(0)
            android.util.Log.d("MainActivity", "✓ 第一个Tab视图类型: ${titleView?.let { it::class.java.name }}")
            android.util.Log.d("MainActivity", "✓ 是否为TabLayout.Tab: ${titleView is TabLayout.Tab}")

            // 验证插件状态
            val isEnabled = stickyBottomTabPlugin?.isEnabled() ?: false
            android.util.Log.d("MainActivity", "✓ 插件启用状态: $isEnabled")

            android.util.Log.d("MainActivity", "🎉 插件兼容性验证完成 - FrameLayout基础架构正常！")

        } catch (e: Exception) {
            android.util.Log.e("MainActivity", "插件兼容性验证失败", e)
        }
    }


    
    override fun onDestroy() {
        super.onDestroy()
        // 销毁插件，释放资源
        stickyBottomTabPlugin?.destroy()
    }
    
    /**
     * ViewPager适配器
     */
    private class DemoFragmentAdapter(
        fragmentActivity: FragmentActivity,
        private val tabTitles: List<String>
    ) : FragmentStateAdapter(fragmentActivity) {
        
        override fun getItemCount(): Int = tabTitles.size
        
        override fun createFragment(position: Int): Fragment {
            return DemoFragment.newInstance(tabTitles[position], position)
        }
    }
}
