package com.ttv.demo

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.fragment.app.Fragment

class ContentFragment : Fragment() {

    private var tabTitle: String? = null
    private var position: Int = 0

    companion object {
        private const val ARG_TAB_TITLE = "tab_title"
        private const val ARG_POSITION = "position"

        fun newInstance(tabTitle: String, position: Int): ContentFragment {
            val fragment = ContentFragment()
            val args = Bundle()
            args.putString(ARG_TAB_TITLE, tabTitle)
            args.putInt(ARG_POSITION, position)
            fragment.arguments = args
            return fragment
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        arguments?.let {
            tabTitle = it.getString(ARG_TAB_TITLE)
            position = it.getInt(ARG_POSITION)
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.fragment_content, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        val titleTextView = view.findViewById<TextView>(R.id.tv_tab_title)
        titleTextView.text = "$tabTitle 内容页面"
        
        // 可以根据不同的tab位置显示不同的内容
        setupContentForTab(view, position)
    }

    private fun setupContentForTab(view: View, position: Int) {
        // 根据不同的tab位置可以设置不同的内容
        // 这里只是简单的演示，实际项目中可以加载不同的数据
        when (position) {
            0 -> {
                // 推荐内容
            }
            1 -> {
                // 关注内容
            }
            2 -> {
                // 热门内容
            }
            // ... 其他tab的内容
        }
    }
}
