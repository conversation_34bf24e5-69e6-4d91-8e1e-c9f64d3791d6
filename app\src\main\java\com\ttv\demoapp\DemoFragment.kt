package com.ttv.demoapp

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.fragment.app.Fragment
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView

/**
 * 演示Fragment
 * 用于展示长列表内容，触发滚动效果
 */
class DemoFragment : Fragment() {
    
    private var tabTitle: String? = null
    private var position: Int = 0
    
    companion object {
        private const val ARG_TITLE = "title"
        private const val ARG_POSITION = "position"
        
        fun newInstance(title: String, position: Int): DemoFragment {
            val fragment = DemoFragment()
            val args = Bundle()
            args.putString(ARG_TITLE, title)
            args.putInt(ARG_POSITION, position)
            fragment.arguments = args
            return fragment
        }
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        arguments?.let {
            tabTitle = it.getString(ARG_TITLE)
            position = it.getInt(ARG_POSITION)
        }
    }
    
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.fragment_demo, container, false)
    }
    
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        val titleTextView = view.findViewById<TextView>(R.id.tv_title)
        val recyclerView = view.findViewById<RecyclerView>(R.id.recycler_view)
        
        titleTextView.text = "$tabTitle 页面"
        
        // 设置RecyclerView
        setupRecyclerView(recyclerView)
    }
    
    /**
     * 设置RecyclerView，创建长列表用于演示滚动效果
     */
    private fun setupRecyclerView(recyclerView: RecyclerView) {
        recyclerView.layoutManager = LinearLayoutManager(context)
        
        // 创建演示数据
        val items = mutableListOf<String>()
        for (i in 1..50) {
            items.add("$tabTitle - 列表项 $i")
        }
        
        recyclerView.adapter = DemoAdapter(items)
    }
    
    /**
     * RecyclerView适配器
     */
    private class DemoAdapter(private val items: List<String>) : 
        RecyclerView.Adapter<DemoAdapter.ViewHolder>() {
        
        class ViewHolder(view: View) : RecyclerView.ViewHolder(view) {
            val textView: TextView = view.findViewById(R.id.tv_item)
        }
        
        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
            val view = LayoutInflater.from(parent.context)
                .inflate(R.layout.item_demo, parent, false)
            return ViewHolder(view)
        }
        
        override fun onBindViewHolder(holder: ViewHolder, position: Int) {
            holder.textView.text = items[position]
        }
        
        override fun getItemCount(): Int = items.size
    }
}
