# 吸底Tab插件演示Demo

这是一个基于现有吸底Tab插件的演示应用，展示了插件的核心功能和使用方法。

## 功能特性

### 🎯 核心功能
- **智能吸底显示**: 当原始Tab滚出屏幕底部时，自动在屏幕底部显示吸底Tab
- **流畅动画效果**: 支持平滑的滑入/滑出动画，提升用户体验
- **一键回到原位**: 点击吸底Tab后自动滚动到原始Tab位置
- **状态完全同步**: 吸底Tab与原始Tab保持选中状态同步
- **即插即用**: 简单的API设计，易于集成

### 🔧 技术特性
- **导航栏自适应**: 自动检测并适配系统导航栏
- **多种Tab支持**: 支持TabLayout和自定义Tab组件
- **内存安全**: 完善的生命周期管理，防止内存泄漏
- **高度可配置**: 丰富的配置选项满足不同需求

## Demo演示内容

### 📱 界面结构
```
CoordinatorLayout (根容器)
├── AppBarLayout (可滚动头部)
│   ├── Toolbar (工具栏)
│   ├── 头部内容区域 (200dp高度)
│   └── CoIndicator (Tab导航)
└── ViewPager2 (内容区域)
    ├── 首页Fragment
    ├── 发现Fragment  
    ├── 消息Fragment
    ├── 我的Fragment
    └── 设置Fragment
```

### 🎮 交互演示
1. **滚动触发**: 向下滚动页面，当Tab滚出屏幕时自动显示吸底Tab
2. **动画效果**: 吸底Tab从底部平滑滑入，带有优雅的动画效果
3. **点击回到**: 点击吸底Tab任意标签，页面自动滚动回到原始Tab位置
4. **状态同步**: 切换ViewPager页面时，吸底Tab选中状态实时同步

### 📊 数据展示
- 每个Fragment包含50个列表项，提供充足的滚动内容
- 不同Tab页面显示不同的内容，便于观察状态同步效果

## 使用方法

### 1. 基本集成
```kotlin
// 创建插件实例
val plugin = StickyBottomTabPluginFactory.createForCoIndicator(
    context = this,
    coordinatorLayout = coordinatorLayout,
    appBarLayout = appBarLayout,
    coIndicator = coIndicator,
    viewPager = viewPager,
    tabTitles = tabTitles
)

// 启用插件
plugin.enable()
```

### 2. 高级配置
```kotlin
val plugin = StickyBottomTabPluginFactory.createForCoIndicator(
    context = this,
    coordinatorLayout = coordinatorLayout,
    appBarLayout = appBarLayout,
    coIndicator = coIndicator,
    viewPager = viewPager,
    tabTitles = tabTitles,
    animationDuration = 300L,        // 动画时长
    scrollToPosition = 0.33f,        // 滚动位置比例
    autoDetectNavigationBar = true,  // 自动检测导航栏
    bottomMargin = 0,                // 底部边距
    horizontalMargin = 0             // 水平边距
)
```

### 3. 生命周期管理
```kotlin
override fun onDestroy() {
    super.onDestroy()
    // 销毁插件，释放资源
    plugin?.destroy()
}
```

## 运行Demo

1. 打开Android Studio
2. 导入项目
3. 运行应用
4. 向下滚动页面观察吸底Tab效果
5. 点击吸底Tab体验自动滚动功能

## 注意事项

- 确保项目中包含必要的依赖库（ViewPager2、Material Design等）
- 插件需要CoordinatorLayout + AppBarLayout的布局结构
- 建议在Activity的onDestroy中调用plugin.destroy()释放资源

## 技术支持

如有问题或建议，请查看插件源码中的详细注释和文档。
